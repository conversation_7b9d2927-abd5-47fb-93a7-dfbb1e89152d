


菜单查看权限MySql表：
CREATE TABLE `zy_menu_view_auth` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `access_key` varchar(40) DEFAULT NULL COMMENT '接入后台key',
  `uid` bigint(20) DEFAULT NULL COMMENT 'uid',
  `status` int(11) DEFAULT NULL COMMENT '状态 0申请中 1通过 -1驳回',
  `update_time` datetime DEFAULT NULL COMMENT '送审时间或审批时间',
  `expire_time` datetime DEFAULT NULL COMMENT '权限过期时间',
  `pid` varchar(100) DEFAULT NULL COMMENT '审批流-流程ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `zy_menu_view_auth_pk_2` (`access_key`,`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='查看菜单权限'