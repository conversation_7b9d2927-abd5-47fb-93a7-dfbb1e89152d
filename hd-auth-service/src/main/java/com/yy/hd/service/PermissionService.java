package com.yy.hd.service;

import com.alibaba.fastjson.JSON;
import com.yy.hd.config.AppConfig;
import com.yy.hd.exception.BizCode;
import com.yy.hd.exception.BizException;
import com.yy.hd.model.CheckResultBo;
import com.yy.hd.model.Logical;
import com.yy.hd.model.TreeNodeBo;
import com.yy.hd.model.bo.AuthAccessBo;
import com.yy.hd.model.bo.PlaceholderPermissionBo;
import com.yy.hd.model.bo.UserPermissionBo;
import com.yy.hd.model.vo.ApplyAuthViewVo;
import com.yy.hd.model.vo.Response;
import com.yy.hd.model.web.PermissionCheckReq;
import com.yy.hd.persist.mapper.*;
import com.yy.hd.persist.model.AuthAccessKey;
import com.yy.hd.persist.model.AuthMenu;
import com.yy.hd.persist.model.AuthPermission;
import com.yy.hd.redis.RedisEvent;
import com.yy.hd.redis.RedisPublisher;
import com.yy.hd.redis.event.UserPermissionChangedEvent;
import com.yy.hd.util.EnvUtil;
import com.yy.hd.util.PathMatchUtil;
import com.yy.hd.util.TreeUtil;
import com.yy.java.component.util.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ThreadUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2025/1/16 21:21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PermissionService implements InitializingBean {

    /** 默认提取menuUri 的正则 */
    private static final Pattern DEFAULT_MENU_URI_PATTERN = Pattern.compile("(?i)https?://[^/]+(:\\d+)?(/[^?]+)(?=\\?|#|$)");

    private final AppConfig appConfig;

    private final AuthAccessKeyExtMapper authAccessKeyExtMapper;
    private final AuthMenuExtMapper authMenuExtMapper;
    private final AuthPermissionExtMapper authPermissionExtMapper;
    private final AuthUserPermissionExtMapper authUserPermissionExtMapper;
    private final AuthSupperUserExtMapper authSupperUserExtMapper;

    private final BossPermissionService bossPermissionService;
    private final ModelConverterService modelConverterService;
    private final MenuUriService menuUriService;
    private final RedisPublisher redisPublisher;

    private final RestTemplate restTemplate;

    /** 用户权限 map<accessKey_uid, UserPermissionBo> */
    private final Map<String, UserPermissionBo> userPermissions = new ConcurrentHashMap<>();

    /** 接入业务列表 */
    private List<AuthAccessBo> authAccessBoList;

    private static final String BOSS_SERVER = EnvUtil.isProd() ? "https://jyboss.yy.com" : "https://jyboss-test.yy.com";

    @Override
    public void afterPropertiesSet() throws Exception {
        refreshAuthAccessBoList();
    }

    /**
     * 刷新业务、菜单、权限缓存
     */
    @Scheduled(fixedRate = 15000)
    public void refreshAuthAccessBoList() {
        long startTime = System.currentTimeMillis();
        log.info("refresh start");
        List<AuthAccessKey> accessKeyList = authAccessKeyExtMapper.listAll();
        this.authAccessBoList = buildAuthAccessBoList(accessKeyList);
        log.info("refresh finished accessKeyCnt:{} cost:{}", this.authAccessBoList.size(), (System.currentTimeMillis() - startTime));
    }

    private List<AuthAccessBo> buildAuthAccessBoList(List<AuthAccessKey> accessKeyList) {
        if (CollectionUtils.isEmpty(accessKeyList)) {
            return new ArrayList<>();
        }
        Set<String> accessKeys = accessKeyList.stream().map(AuthAccessKey::getAccessKey).collect(Collectors.toSet());
        // 查询所有菜单
        var allMenuList = authMenuExtMapper.selectByAccessKeys(accessKeys);
        // 按照 access 业务进行分组
        Map<String, List<AuthMenu>> accessKey2menus = new HashMap<>(accessKeyList.size());
        for (var menu : allMenuList) {
            String accessKey = menu.getAccessKey();
            // 将菜单uri统一处理成 / 开头的
            menu.setUrl(formatMenuUri(menu.getUrl()));
            List<AuthMenu> menuList = accessKey2menus.computeIfAbsent(accessKey, k -> new ArrayList<>());
            menuList.add(menu);
        }

        // 查询所有权限
        List<AuthPermission> allPermissionList = authPermissionExtMapper.selectByAccessKeys(accessKeys);
        // 权限和菜单关系 map<accessKey, map<permission, List<menuId>>>
        Map<String, Map<String, Set<Long>>> permission2menuIds = new HashMap<>(allPermissionList.size());
        // 有占位符的权限 map<accessKey, set<PlaceholderPermissionBo>>
        Map<String, Set<PlaceholderPermissionBo>> placeholderPermissions = new HashMap<>();
        // 权限代码和权限列表关系 map<accessKey, map<permission, List<AuthPermission>>>
        Map<String, Map<String, List<AuthPermission>>> permission2permissions = new HashMap<>(allPermissionList.size());
        // 权限 map<accessKey, map<menuId, List<AuthPermission>>>
        Map<String, Map<Long, List<AuthPermission>>> permissionMap = new HashMap<>(accessKeyList.size());
        // 权限 map<accessKey, map<menuId, Set<PermissionId>>>
        Map<String, Map<Long, Set<Long>>> permissionIdsMap = new HashMap<>(accessKeyList.size());
        // 菜单和资源关系 map<accessKey, map<menuId, SortedSet<String>>>
        Map<String, Map<Long, SortedSet<String>>> menuId2resources = new HashMap<>();

        for (var permission : allPermissionList) {
            Map<Long, List<AuthPermission>> map = permissionMap.computeIfAbsent(permission.getAccessKey(), k -> new HashMap<>());
            List<AuthPermission> permissionList = map.computeIfAbsent(permission.getMid(), k -> new ArrayList<>());
            permissionList.add(permission);

            Map<Long, Set<Long>> pmap = permissionIdsMap.computeIfAbsent(permission.getAccessKey(), k -> new HashMap<>());
            Set<Long> permissionIdList = pmap.computeIfAbsent(permission.getMid(), k -> new HashSet<>());
            permissionIdList.add(permission.getId());

            PlaceholderPermissionBo pp = new PlaceholderPermissionBo(permission.getPermission());
            String tidyPermission = pp.getTidyPermission();

            Map<String, Set<Long>> menuIds = permission2menuIds.computeIfAbsent(permission.getAccessKey(), k -> new HashMap<>());
            Set<Long> refMenuIds = menuIds.computeIfAbsent(tidyPermission, k -> new HashSet<>());
            refMenuIds.add(permission.getMid());

            Set<PlaceholderPermissionBo> placeholderPermissionBoSet = placeholderPermissions.computeIfAbsent(permission.getAccessKey(), k -> new HashSet<>());
            if (pp.hasPlaceholder()) {
                placeholderPermissionBoSet.add(pp);
            }

            Map<String, List<AuthPermission>> permissions = permission2permissions.computeIfAbsent(permission.getAccessKey(), k -> new HashMap<>());
            List<AuthPermission> refPermissions = permissions.computeIfAbsent(tidyPermission, k -> new ArrayList<>());
            refPermissions.add(permission);

            Map<Long, SortedSet<String>> mid2res = menuId2resources.computeIfAbsent(permission.getAccessKey(), k -> new HashMap<>());
            SortedSet<String> resources = mid2res.computeIfAbsent(permission.getMid(), k -> new TreeSet<>());
            resources.add(tidyPermission);
        }

        List<AuthAccessBo> accessBos = new ArrayList<>(accessKeyList.size());
        for (var accessKey : accessKeyList) {
            var bo = modelConverterService.toBoAuthAccess(accessKey);
            accessBos.add(bo);
            // 获取该业务下的所有菜单
            List<AuthMenu> menus = accessKey2menus.get(bo.getAccessKey());
            if (CollectionUtils.isEmpty(menus)) {
                continue;
            }
            // 排序，按照 orderNo 倒序
            menus.sort((m1, m2) -> {
                int order1 = m1.getOrderNo() == null ? 0 : m1.getOrderNo();
                int order2 = m2.getOrderNo() == null ? 0 : m2.getOrderNo();
                return order2 - order1;
            });

            // 构造树结构
            List<TreeNodeBo<AuthMenu>> menuRootList = TreeUtil.build(menus, AuthMenu::getId, AuthMenu::getPid, null);
            bo.setMenuRootList(menuRootList);

            Map<Long, TreeNodeBo<AuthMenu>> menuId2menu = new HashMap<>(menus.size());
            Map<String, TreeNodeBo<AuthMenu>> menuUri2menu = new HashMap<>(menus.size());
            TreeUtil.traversal(menuRootList, TreeNodeBo::getChildren, new BiConsumer<TreeNodeBo<AuthMenu>, Integer>() {
                @Override
                public void accept(TreeNodeBo<AuthMenu> node, Integer level) {
                    menuId2menu.put(node.getData().getId(), node);

                    String menuUri = node.getData().getUrl();
                    if (menuUri != null && !menuUri.isBlank()) {
                        menuUri2menu.put(menuUri, node);
                        menuUri2menu.put(menuUriService.getFixMenuUri(menuUri), node);
                    }
                }
            });

            bo.setMenuId2menu(menuId2menu);
            bo.setMenuUri2menu(menuUri2menu);

            // 权限
            bo.setPermission2menuIds(permission2menuIds.getOrDefault(bo.getAccessKey(), Collections.emptyMap()));

            // 菜单id对应的权限id映射
            bo.setMenu2permissionIds(permissionIdsMap.getOrDefault(bo.getAccessKey(), new HashMap<>()));

            // 有占位符的权限
            bo.setPlaceholderPermissions(placeholderPermissions.getOrDefault(bo.getAccessKey(), new HashSet<>()));

            // 菜单对应的权限列表
            bo.setMenuId2permissions(permissionMap.getOrDefault(bo.getAccessKey(), new HashMap<>()));
        }
        return accessBos;
    }

    /**
     * 刷新用户权限
     */
    @Scheduled(fixedRate = 15000, initialDelay = 15000)
    public void refreshUserPermission() {
        long startTime = System.currentTimeMillis();
        log.info("refresh user permission start");

        // 淘汰的数量
        int removed = 0;
        int refreshCnt = 0;
        if (!userPermissions.isEmpty()) {
            List<UserPermissionBo> candidateList = new ArrayList<>(userPermissions.values());
            if (CollectionUtils.isNotEmpty(candidateList)) {
                long now = System.currentTimeMillis();
                long expiredMillis = appConfig.getUserPermissionExpiredMillis();
                for (var item : candidateList) {
                    String key = getUserPermissionCacheKey(item.getAccessKey(), item.getUid());
                    if (item.getLastAccess() > 0 && now - item.getLastAccess() >= expiredMillis) {
                        removed++;
                        userPermissions.remove(key);
                        continue;
                    }
                    refreshCnt++;
                    var bo = getUserPermissionWithoutCache(item.getAccessKey(), item.getUid());
                    userPermissions.put(key, bo);
                }
            }
        }
        log.info("refresh user permission finished, refreshCnt:{} removed:{} cost:{}", refreshCnt, removed, System.currentTimeMillis() - startTime);
    }

    @Async
    public void publishUserPermissionChangedEvent(String accessKey, long uid) {
        ThreadUtils.sleepQuietly(Duration.ofSeconds(1));
        UserPermissionChangedEvent event = new UserPermissionChangedEvent();
        event.setAccessKey(accessKey);
        event.setUid(uid);
        redisPublisher.publish(RedisEvent.Topic.USER_PERMISSION_CHANGED, JsonUtils.serialize(event));
    }

    /**
     * 用户申请权限通过
     */
    public void onUserPermissionChanged(String topic, RedisEvent redisEvent) {
        log.info("onUserPermissionChanged topic:{} event:{}", topic, JsonUtils.serialize(redisEvent));
        UserPermissionChangedEvent event = JsonUtils.deserialize(redisEvent.getData(), UserPermissionChangedEvent.class);

        if (bossPermissionService.onUserPermissionChanged(topic, event)) {
            return;
        }

        String key = getUserPermissionCacheKey(event.getAccessKey(), event.getUid());
        var bo = getUserPermissionWithoutCache(event.getAccessKey(), event.getUid());
        userPermissions.put(key, bo);
        log.info("refreshUserPermission topic:{} accessKey:{} uid:{}", topic, event.getAccessKey(), event.getUid());
    }

    /**
     * 检查是否有权限，基于这样的一个认知：
     * 1. 请求过来的，都应该正常从管理后台请求的（都会待referer，都携带菜单信息），否则，那就不应该请求到这里（业务侧应拦截掉：比如一些测试接口）
     */
    public CheckResultBo hasPermission(PermissionCheckReq req) {
        log.info("checkPermission uid:{} req:{}", req.getUid(), JSON.toJSONString(req));
        if (!appConfig.isPermissionLimitUid(req.getUid())) {
            log.info("skip un need limit uid:{} req:{}", req.getUid(), JSON.toJSONString(req));
            return CheckResultBo.passed("un need limit", null, null);
        }

        if (StringUtils.isBlank(req.getAccessKey()) && StringUtils.isBlank(req.getReferer())) {
            // 没有 referer，直接拦截
            if (!EnvUtil.isProd()) {
                // 非生产环境，允许忽略
                if (appConfig.isIgnoreNoRefererDomain(req.getHostname())) {
                    log.info("skip no referer and ignore domain, req:{}", JSON.toJSONString(req));
                    return CheckResultBo.passed("no referer and ignore domain", null, null);
                }
            }
            List<String> accessKeys = req.getAccessKeys();
            if (CollectionUtils.isNotEmpty(accessKeys)) {
                accessKeys = accessKeys.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(accessKeys)) {
                return CheckResultBo.unsupported("missing referer");
            }
            req.setAccessKeys(accessKeys);
        }



        // 计算权限代码
        List<String> permissions = new ArrayList<>();
        if (StringUtils.isNotBlank(req.getPermission())) {
            permissions = Arrays.stream(req.getPermission().split(",")).filter(StringUtils::isNotBlank).toList();
        }
        if (CollectionUtils.isEmpty(permissions)) {
            permissions = StringUtils.isBlank(req.getPath()) ? Collections.emptyList() : Collections.singletonList(req.getPath());
        }
        // 参数不对，没有权限信息，拒绝
        if (CollectionUtils.isEmpty(permissions)) {
            log.info("reject no permission input, req:{}", JSON.toJSONString(req));
            return CheckResultBo.passed("no permission input", null, "");
        }

        var ak = getAuthAccessKey(req);
        List<AuthAccessBo> accessKeys = new ArrayList<>();
        if (ak != null) {
            accessKeys.add(ak);
        } else if (CollectionUtils.isNotEmpty(req.getAccessKeys())) {
            for (var item : req.getAccessKeys()) {
                var bo = getAuthAccessKey(item, null);
                if (bo != null) {
                    accessKeys.add(bo);
                }
            }
        }
        if (CollectionUtils.isEmpty(accessKeys)) {
            if (!EnvUtil.isProd() && StringUtils.isNotBlank(req.getReferer()) && appConfig.isIgnoreNoAccessKeyReferer(req.getReferer())) {
                log.info("skip no match accessKey by in whiteList, req:{}", JSON.toJSONString(req));
                return CheckResultBo.passed("no match accessKey by in whiteList", null, "");
            }
            // 还未接入的业务
            log.info("reject no match accessKey, req:{}", JSON.toJSONString(req));
            return CheckResultBo.reject("no match accessKey", null, "");
        }

        for(AuthAccessBo akItem: accessKeys){
            if(isSuperUser(req.getUid(), akItem.getAccessKey())){
                log.info("isSuperUser, uid:{}, ak:{}", req.getUid(), ak);
                return CheckResultBo.passed("supper user", akItem.getAccessKey(), null);
            }
        }

        String menuUri = null;
        // 遍历每个 access，找到第一个有权限的
        for (var accessKey : accessKeys) {
            if (bossPermissionService.isTargetAccessKey(accessKey.getAccessKey())) {
                req.setAccessKey(accessKey.getAccessKey());
                var checkRet = bossPermissionService.hasPermission(req);
                if (checkRet.isSupport()) {
                    return checkRet;
                }
                continue;
            }
            menuUri = extractMenuUri(accessKey, req.getReferer());
            TreeNodeBo<AuthMenu> menuNode = getAccessMenu(accessKey, menuUri);
            long menuId = menuNode == null || menuNode.getData() == null ? 0 : menuNode.getData().getId();
            // 过滤掉无需检查的权限代码
            permissions = permissions.stream().filter(permission -> {
                if (PathMatchUtil.isAnyMatch(permission, accessKey.getIgnorePatterns())) {
                    return false;
                }
                return true;
            }).distinct().toList();

            if (CollectionUtils.isEmpty(permissions)) {
                // 都不是需要验证的权限
                log.info("skip permission not in controlled, hitAccessKey:{} req:{}", accessKey.getAccessKey(), JSON.toJSONString(req));
                return CheckResultBo.passed("permission not in controlled", accessKey.getAccessKey(), menuUri);
            }

            // 获取用户权限
            var userPermissionBo = getUserPermission(accessKey.getAccessKey(), req.getUid());
            long curMillis = System.currentTimeMillis();

            Logical logical = Optional.ofNullable(req.getLogical()).orElse(Logical.AND);
            boolean passed;
            if (Logical.AND.equals(logical)) {
                passed = permissions.stream().allMatch(p -> hasUserPermission(curMillis, accessKey, userPermissionBo, p, req.getPath(), menuId));
            } else {
                passed = permissions.stream().anyMatch(p -> hasUserPermission(curMillis, accessKey, userPermissionBo, p, req.getPath(), menuId));
            }
            if (passed) {
                log.info("passed menuId:{} permissions:{}", menuId, permissions);
                return CheckResultBo.passed("passed", accessKey.getAccessKey(), menuUri);
            }
        }
        log.info("reject permissions:{}", permissions);
        return CheckResultBo.reject("no permission", accessKeys.getFirst().getAccessKey(), menuUri);
    }

    /**
     * 检查是否有特定资源的访问权限
     */
    private boolean hasUserPermission(long checkTimeMillis, AuthAccessBo accessKey, UserPermissionBo userPermissionBo, String resource, String requestPath, long menuId) {
        Set<Long> menuIds = accessKey.getResourceMenuIds(resource);
        if (CollectionUtils.isEmpty(menuIds)) {
            // 按照请求地址作为permission去查找
            if (!Objects.equals(requestPath, resource)) {
                menuIds = accessKey.getResourceMenuIds(requestPath);
            }
            // 不属于任何菜单，按道理无需请求到这里，应该前面就应该拦截掉,不应该调用本接口，否则就是漏洞（通过别的业务访问别的另一个业务接口，不应该访问）
            if (CollectionUtils.isEmpty(menuIds)) {
                return false;
            }
        }

        // 虽然找到了，但是不是在同一个菜单下，那么根据请求路径来获取菜单
        if (menuId > 0 && !menuIds.contains(menuId)) {
            // 按照请求地址作为permission去查找
            if (!Objects.equals(requestPath, resource)) {
                Set<Long> requestPathMenuIds = accessKey.getResourceMenuIds(requestPath);
                if (CollectionUtils.isNotEmpty(requestPathMenuIds)) {
                    menuIds = requestPathMenuIds;
                }
            }
        }

        if (menuId > 0) {
            // referer有识别到菜单，校验当前菜单
            return menuIds.contains(menuId) && userPermissionBo.hasPermission(checkTimeMillis, menuId);
        }
        for (var mid : menuIds) {
            // referer未识别到菜单, 其中一个菜单有权限即可
            if (userPermissionBo.hasPermission(checkTimeMillis, mid)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 统一格式化menuUri，设置 / 开头, 便于比较
     * admin/user -> /admin/user
     * //admin//user -> /admin/user
     *
     * @param menuUri 菜单URI
     */
    private String formatMenuUri(String menuUri) {
        if (StringUtils.isBlank(menuUri)) {
            return null;
        }
        menuUri = menuUri.replaceAll("/+", "/");
        if (!menuUri.startsWith("/")) {
            menuUri = "/" + menuUri;
        }
        return menuUri;
    }

    public TreeNodeBo<AuthMenu> getAccessMenu(AuthAccessBo accessKey, String menuUri) {
        if (StringUtils.isBlank(menuUri)) {
            return null;
        }
        return accessKey.getMenuNodeByUri(menuUri);
    }

    public String extractMenuUri(AuthAccessBo accessKey, String referer) {
        if (StringUtils.isBlank(referer)) {
            return null;
        }
        // 追吖产品后台：https://zhuiya-test.yy.com/admin-static/#/dateRoomGameSetting  -> /admin-static/#/dateRoomGameSetting -> (/admin-static/#/[^?]+)(?=\?|#|$)
        // 追吖技术后台：https://zhuiya-test.yy.com/admin-tech/#/clientConfig -> /admin-tech/#/clientConfig -> (/admin-tech/#/[^?]+)(?=\?|#|$)
        // jyboss产品后台：https://jyboss-test.yy.com/n/consume/index -> /consume/index -> /n(/[^?#]+)(?=\?|#|$)
        // jyboss技术后台：https://jydev-test.yy.com/n/consume/index -> /consume/index -> /n(/[^?#]+)(?=\?|#|$)

        String menuUri = null;
        List<Pattern> menuUriPattern = accessKey.getMenuUriPattern();
        boolean matched = false;
        if (CollectionUtils.isNotEmpty(menuUriPattern)) {
            for (var pattern : menuUriPattern) {
                var matcher = pattern.matcher(referer);
                if (matcher.find()) {
                    int group = Optional.ofNullable(accessKey.getMenuUriPatternGroup()).orElse(0);
                    group = group <= 0 ? 1 : group;
                    menuUri = matcher.group(group);
                    matched = true;
                    break;
                }
            }
        }

        // 未能匹配到，直接提取请求路径，不包含参数，但是包含hash值
        if (!matched) {
            var matcher = DEFAULT_MENU_URI_PATTERN.matcher(referer);
            if (matcher.find()) {
                menuUri = matcher.group(2);
            }
        }

        menuUri = formatMenuUri(menuUri);
        return menuUri;
    }

    /**
     * 获取用户权限
     *
     * @param accessKey 业务
     * @param uid       uid
     * @return 返回用户权限信息
     */
    public UserPermissionBo getUserPermission(String accessKey, long uid) {
        String cacheKey = getUserPermissionCacheKey(accessKey, uid);

        var bo = userPermissions.get(cacheKey);
        if (bo != null) {
            bo.setLastAccess(System.currentTimeMillis());
            return bo;
        }
        bo = getUserPermissionWithoutCache(accessKey, uid);
        userPermissions.put(cacheKey, bo);
        log.info("refreshUserPermission uid:{} accessKey:{} uid:{}", uid, accessKey, uid);
        return bo;
    }

    private String getUserPermissionCacheKey(String accessKey, long uid) {
        return String.format("%s_%d", accessKey, uid);
    }

    private UserPermissionBo getUserPermissionWithoutCache(String accessKey, long uid) {
        var accessBo = getAuthAccessKey(accessKey, null);
        Date now = new Date();

        var bo = new UserPermissionBo();
        bo.setUid(uid);
        bo.setAccessKey(accessKey);
        bo.setLastAccess(now.getTime());
        bo.setLastRefresh(now.getTime());
        if (accessBo == null) {
            return bo;
        }

        Map<Long, Long> menu2expireAt = new HashMap<>();

        Set<Long> existsMenuIds = new HashSet<>(menu2expireAt.keySet());
        // 获取用户所有权限（兼容旧逻辑）
        var permissionList = isSuperUser(uid, accessKey)? authUserPermissionExtMapper.getAllEffectivePermissions(accessKey) : authUserPermissionExtMapper.getEffectivePermissions(uid, accessKey, now);
        if (CollectionUtils.isNotEmpty(permissionList)) {
            for (var p : permissionList) {
                if (p.getMenuId() == null || p.getMenuId() <= 0) {
                    continue;
                }
                long expireAt = p.getExpireTime() == null ? 0 : p.getExpireTime().getTime();
                if (existsMenuIds.contains(p.getMenuId())) {
                    // 有配置菜单权限
                    long oExpireAt = Optional.ofNullable(menu2expireAt.get(p.getMenuId())).orElse(0L);
                    if (expireAt <= 0 && oExpireAt > 0) {
                        menu2expireAt.put(p.getMenuId(), expireAt);
                    }
                    if (oExpireAt > 0 && expireAt > oExpireAt) {
                        menu2expireAt.put(p.getMenuId(), expireAt);
                    }
                } else {
                    // 菜单表不存在，需要插入
                    menu2expireAt.put(p.getMenuId(), expireAt);
                }
            }
        }

        // 遍历菜单的所有父级
        if (!menu2expireAt.isEmpty()) {
            List<Long> menuIds = new ArrayList<>(menu2expireAt.keySet());
            for (var menuId : menuIds) {
                var expireAt = menu2expireAt.get(menuId);
                var parentIds = accessBo.getParentMenuIds(menuId);
                for (var parentId : parentIds) {
                    if (expireAt <= 0 || !menu2expireAt.containsKey(parentId)) {
                        menu2expireAt.put(parentId, expireAt);
                    } else {
                        Long oExpireAt = menu2expireAt.get(parentId);
                        if (oExpireAt != null && oExpireAt < expireAt) {
                            menu2expireAt.put(parentId, expireAt);
                        }
                    }
                }
            }
        }
        bo.setMenu2expireAt(menu2expireAt);
        return bo;
    }

    public AuthAccessBo getAuthAccessKey(PermissionCheckReq req) {
        String accessKey = bossPermissionService.resolveAccessKey(req);
        if (StringUtils.isNotBlank(accessKey)) {
            req.setAccessKey(accessKey);
        }
        return getAuthAccessKey(req.getAccessKey(), req.getReferer());
    }

    /**
     * 根据请求信息获取接入业务信息
     * 根据 access_key 中定义的 referer_patterns 来进行匹配
     */
    public AuthAccessBo getAuthAccessKey(String accessKey, String referer) {
        return getAuthAccessKey(accessKey, referer, false);
    }

    /**
     * 根据请求信息获取接入业务信息
     * 根据 access_key 中定义的 referer_patterns 来进行匹配
     */
    public AuthAccessBo getAuthAccessKey(String accessKey, String referer, boolean disabledCache) {
        var accessKeyList = authAccessBoList;
        if (CollectionUtils.isEmpty(accessKeyList)) {
            return null;
        }
        boolean blankReferer = StringUtils.isBlank(referer);
        for (var ak : accessKeyList) {
            if (Objects.equals(ak.getAccessKey(), accessKey)) {
                return resolveAuthAccessBo(ak, disabledCache);
            }
            if (blankReferer) {
                continue;
            }
            if (ak.isRefererMatched(referer)) {
                return resolveAuthAccessBo(ak, disabledCache);
            }
        }
        if (disabledCache) {
            var bo = new AuthAccessBo();
            bo.setAccessKey(accessKey);
            return resolveAuthAccessBo(bo, true);
        }
        return null;
    }

    private AuthAccessBo resolveAuthAccessBo(AuthAccessBo bo, boolean disabledCache) {
        if (disabledCache) {
            // 禁用缓存，直接从DB中进行构建
            AuthAccessKey authAccessKey = this.authAccessKeyExtMapper.selectByPrimaryKey(bo.getAccessKey());
            List<AuthAccessBo> list = buildAuthAccessBoList(List.of(authAccessKey));
            if (CollectionUtils.isNotEmpty(list)) {
                AuthAccessBo dbBo = list.getFirst();
                if (dbBo != null) {
                    return dbBo;
                }
            }
        }
        return bo;
    }

    /**
     * 获取申请权限页面查看权限
     */
    public ApplyAuthViewVo getApplyAuthView(long uid, String accessKey, String referer) {
        var ak = getAuthAccessKey(accessKey, referer);
        if (ak == null) {
            throw new BizException(BizCode.BAD_REQUEST, "无法识别业务");
        }
        if (Boolean.FALSE.equals(ak.getNeedApplyView())) {
            ApplyAuthViewVo rsp = new ApplyAuthViewVo();
            rsp.setAccessible(true);
            return rsp;
        }
        accessKey = ak.getAccessKey();
        String checkUrl = String.format("%s/boss_menu/query_view_auth?uid=%d&app=%s", BOSS_SERVER, uid, accessKey);
        var rsp = restTemplate.getForObject(checkUrl, ApplyAuthViewVo.class);
        if (rsp != null && rsp.getList() != null && rsp.getList().getExpireTime() != null) {
            rsp.setAccessible(rsp.getList().getExpireTime() * 1000 > System.currentTimeMillis());
        }
        return rsp;
    }

    /**
     * 申请查看授权页面（申请 【申请菜单权限】的权限）
     */
    public Response applyAuthView(long uid, String accessKey, String referer) {
        var ak = getAuthAccessKey(accessKey, referer);
        if (ak == null) {
            throw new BizException(BizCode.BAD_REQUEST, "无法识别业务");
        }
        accessKey = ak.getAccessKey();
        String checkUrl = String.format("%s/boss_menu/apply_view_auth?uid=%d&app=%s&name=%s",
                BOSS_SERVER, uid, accessKey, StringUtils.isBlank(ak.getAccessName()) ? accessKey : ak.getAccessName());
        var rsp = restTemplate.getForObject(checkUrl, Response.class);
        log.info("applyAuthView uid:{} accessKey:{} rsp:{}", uid, accessKey, JsonUtils.serialize(rsp));
        return rsp;
    }

    public boolean isSuperUser(long uid, String accessKey) {
        if (!appConfig.isSupportSupperSuer()) {
            return false;
        }
        List<Long> uids = authSupperUserExtMapper.selectSupperUsers();
        return CollectionUtils.isNotEmpty(uids) && uids.contains(uid);
    }
}
